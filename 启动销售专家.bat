@echo off
chcp 65001 >nul
title 销售专家独立分析工具

echo.
echo ================================================
echo 🤖 销售专家独立分析工具
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 显示Python版本
echo 📋 检查Python环境...
python --version

REM 检查必要文件
if not exist "sales_expert_standalone.py" (
    echo ❌ 未找到 sales_expert_standalone.py 文件
    pause
    exit /b 1
)

if not exist "MCP.env" (
    echo ❌ 未找到 MCP.env 配置文件
    echo 请确保API密钥已正确配置
    pause
    exit /b 1
)

echo ✅ 文件检查通过

echo.
echo 📋 选择运行模式:
echo 1. 完整模式 (推荐) - 提供完整的菜单界面
echo 2. 快速模式 - 使用默认配置快速分析
echo 3. 测试环境 - 检查环境配置
echo 4. 查看帮助 - 显示使用说明
echo 5. 退出
echo.

set /p choice="请选择 (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动完整模式...
    python sales_expert_standalone.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动快速模式...
    python run_sales_expert.py quick
) else if "%choice%"=="3" (
    echo.
    echo 🧪 运行环境测试...
    python test_sales_expert.py
) else if "%choice%"=="4" (
    echo.
    echo 📖 显示帮助信息...
    python run_sales_expert.py help
) else if "%choice%"=="5" (
    echo 👋 退出程序
    exit /b 0
) else (
    echo ❌ 无效选择
)

echo.
echo ================================================
echo 程序执行完毕
echo ================================================
pause
