#!/usr/bin/env python3
"""
快速测试脚本 - 验证导入和基本功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_import():
    """测试导入"""
    print("🧪 测试导入...")
    try:
        from sales_expert_standalone import SalesExpertStandalone
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    try:
        from sales_expert_standalone import SalesExpertStandalone
        
        # 创建实例
        sales_expert = SalesExpertStandalone()
        print("✅ 实例创建成功")
        
        # 测试配置获取
        configs = sales_expert.get_model_configs()
        print(f"✅ 获取到 {len(configs)} 个模型配置")
        
        # 显示默认模型
        first_model = list(configs.keys())[0]
        print(f"✅ 默认模型: {first_model}")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    print("🔍 快速测试")
    print("="*30)
    
    import_ok = test_import()
    if import_ok:
        func_ok = test_basic_functionality()
    else:
        func_ok = False
    
    print("\n" + "="*30)
    if import_ok and func_ok:
        print("🎉 测试通过！")
        print("✅ 导入问题已修复")
        print("✅ 默认模型设置为 meta-llama/llama-4-maverick")
    else:
        print("❌ 测试失败")
    print("="*30)

if __name__ == "__main__":
    main()
