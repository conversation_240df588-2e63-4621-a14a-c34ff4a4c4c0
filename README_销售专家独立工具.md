# 🤖 销售专家独立分析工具

基于原有销售专家功能开发的独立命令行工具，无需前端界面，支持默认模板和自定义模板。

## ✨ 功能特点

- 🎯 **独立运行**: 无需启动完整的Web界面，命令行直接使用
- 📋 **模板支持**: 支持默认模板和自定义模板管理
- 🤖 **多模型支持**: 默认使用Llama 4 Maverick，支持Grok、GPT-4o、DeepSeek、通义千问等多种AI模型
- 🔍 **实时搜索**: 集成Tavily搜索工具，获取真实网站推荐
- 💾 **结果保存**: 自动保存分析结果到本地文件
- ⚡ **快速模式**: 提供快速分析模式，一键完成分析

## 📋 环境要求

- Python 3.8+
- 已安装的依赖库（与主程序相同）
- 配置好的API密钥

## 🔧 安装配置

### 1. 确保依赖库已安装
```bash
# 如果还没有安装依赖，请先安装
pip install -r requirements.txt
```

### 2. 配置API密钥
确保 `MCP.env` 文件中包含以下API密钥：

```env
# 必需 - Tavily搜索API
TAVILY_API_KEY=your_tavily_api_key_here

# 推荐 - OpenRouter API（支持Llama 4 Maverick，性能最佳）
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 备选 - xAI Grok模型
GROK_API_KEY=your_grok_api_key_here

# 备选 - OpenAI GPT模型
OPENAI_API_KEY=your_openai_api_key_here

# 可选 - 其他模型
DEEPSEEK_API_KEY=your_deepseek_api_key_here
ALIYUN_API_KEY=your_aliyun_api_key_here
```

## 🚀 使用方法

### 方式一：Windows用户（最简单）
双击运行 `启动销售专家.bat` 文件，选择运行模式。

### 方式二：完整模式（推荐）
```bash
python sales_expert_standalone.py
```

提供完整的菜单界面，包括：
- 使用默认模板分析
- 使用自定义模板分析
- 查看默认模板
- 管理自定义模板
- 模型选择

### 方式三：快速模式
```bash
python run_sales_expert.py quick
```

使用默认配置快速分析，适合简单的一次性分析。

### 方式四：配置检查
```bash
python check_config.py
```

检查环境配置是否正确，生成配置模板。

### 方式五：查看帮助
```bash
python run_sales_expert.py help
```

## 📖 使用示例

### 1. 快速分析示例
```bash
$ python run_sales_expert.py quick

🚀 销售专家快速分析模式
==================================================
📋 请输入分析信息（直接回车使用默认值）:
产品名称 (默认: 化学品合成设备):
产品详细信息 (默认: 应用于生物制药的微流):
目标国家/地区 (默认: 美国):
检索时间段 (默认: 2025年7月):
期望商机数量 (默认: 5):

✅ 产品信息确认:
   产品名称: 化学品合成设备
   产品详情: 应用于生物制药的微流
   目标地区: 美国
   时间段: 2025年7月
   商机数量: 5

🎯 使用默认配置进行分析...
📋 模型: meta-llama/llama-4-maverick (如果可用，否则使用 GPT-4o)
📋 模板: 默认销售专家模板

✅ 已初始化 OPENROUTER 模型: meta-llama/llama-4-maverick
🔧 初始化搜索工具...
✅ Tavily MCP工具初始化成功
🤖 创建销售专家Agent...
🎯 开始执行分析...
⏳ 正在分析中，请稍候...

✅ 分析完成！
📋 销售专家分析结果:
==================================================
[分析结果内容...]
==================================================
💾 分析结果已保存到: analysis_results/sales_analysis_化学品合成设备_美国_20250731_143022.txt
```

### 2. 完整模式示例
```bash
$ python sales_expert_standalone.py

🎉 欢迎使用销售专家独立分析工具！

============================================================
🤖 销售专家独立分析工具
============================================================
1. 使用默认模板进行分析
2. 使用自定义模板进行分析
3. 查看默认模板
4. 管理自定义模板
5. 退出
============================================================

请选择操作 (1-5): 1

🎯 使用默认模板进行分析

📋 选择AI模型:
----------------------------------------
1. meta-llama/llama-4-maverick - Llama 4 Maverick，专为商机分析优化的高性能模型（推荐）
2. grok-4-0709 - xAI Grok-4-0709，最强推理模型
3. openai/gpt-4o - OpenAI GPT-4o，多模态能力强
4. deepseek-reasoner - DeepSeek推理模型，专业的逻辑推理和分析能力
5. qwen-max - 阿里云通义千问最强版本，多模态能力强

请选择模型 (1-5): 1
✅ 已选择模型: meta-llama/llama-4-maverick

📋 请输入产品信息:
------------------------------
产品名称 (默认: 化学品合成设备): 工业机器人
产品详细信息 (默认: 应用于生物制药的微流): 用于汽车制造的焊接机器人
目标国家/地区 (默认: 美国): 德国
检索时间段 (默认: 2025年7月): 2025年8月
期望商机数量 (默认: 5): 10

✅ 产品信息确认:
   产品名称: 工业机器人
   产品详情: 用于汽车制造的焊接机器人
   目标地区: 德国
   时间段: 2025年8月
   商机数量: 10

[分析过程...]
```

## 📁 文件结构

```
├── sales_expert_standalone.py    # 主程序文件
├── run_sales_expert.py          # 快速启动脚本
├── check_config.py              # 配置检查脚本
├── 启动销售专家.bat              # Windows批处理启动脚本
├── 销售专家工具说明.txt          # 快速上手指南
├── MCP.env                       # 环境配置文件
├── sales_expert_templates.json  # 自定义模板存储（自动创建）
├── analysis_results/             # 分析结果目录（自动创建）
│   └── sales_analysis_*.txt     # 分析结果文件
└── README_销售专家独立工具.md    # 详细使用说明
```

## 🎯 默认模板说明

默认模板包含以下网站类别搜索：
1. **综合新闻平台** - 通用商业/行业新闻
2. **垂直领域新闻** - 行业特定新闻和趋势
3. **行业信息网站** - 技术发展、市场分析
4. **行业论坛** - 专业讨论和网络
5. **行业协会网站** - 官方组织和标准
6. **展会信息网站** - 贸易展览和活动
7. **财经新闻网站** - 投资和市场数据

## 🔧 自定义模板

### 创建自定义模板
1. 选择菜单选项 "4. 管理自定义模板"
2. 选择 "2. 创建新模板"
3. 输入模板名称和内容
4. 可使用变量：`{product_name}`, `{product_details}`, `{countries}`

### 模板变量说明
- `{product_name}`: 产品名称（默认：化学品合成设备）
- `{product_details}`: 产品详情（默认：应用于生物制药的微流）
- `{countries}`: 目标国家/地区（默认：美国）
- `{time_period}`: 检索时间段（默认：2025年7月）
- `{opportunities_count}`: 期望商机数量（默认：5）

## 📊 输出结果

分析结果包含：
- 7个类别的网站推荐
- 每个网站的URL、描述和相关性说明
- 搜索摘要和市场洞察
- 自动保存到 `analysis_results/` 目录

## ⚠️ 注意事项

1. **网络连接**: 需要稳定的网络连接进行在线搜索
2. **API配额**: 注意各API服务的使用配额限制
3. **分析时间**: 完整分析通常需要2-5分钟
4. **结果质量**: 结果质量取决于搜索关键词和目标地区的网络资源

## 🐛 故障排除

### 快速诊断
首先运行配置检查脚本：
```bash
python check_config.py
```

### 常见问题

1. **API密钥错误**
   ```
   ❌ 配置错误: TAVILY_API_KEY环境变量是必需的
   ```
   解决：
   - 运行 `python check_config.py` 检查配置
   - 确保 `MCP.env` 文件存在且包含正确的API密钥
   - 如果没有配置文件，会自动生成 `MCP.env.template` 模板

2. **模型初始化失败**
   ```
   ❌ GROK_API_KEY环境变量是必需的
   ```
   解决：
   - 配置对应模型的API密钥
   - 或选择其他可用模型（如GPT-4o）
   - 运行配置检查查看可用模型

3. **搜索工具初始化失败**
   ```
   ❌ MCP工具初始化最终失败
   ```
   解决：
   - 检查网络连接
   - 验证Tavily API密钥是否有效
   - 确保防火墙允许Node.js网络访问

4. **分析超时**
   ```
   ❌ 分析超时，请稍后重试
   ```
   解决：
   - 检查网络连接稳定性
   - 稍后重试（可能是API服务繁忙）
   - 尝试使用不同的AI模型

5. **依赖库缺失**
   ```
   ❌ 导入失败: No module named 'autogen_core'
   ```
   解决：
   - 安装缺失的依赖：`pip install autogen-core autogen-agentchat autogen-ext`
   - 或运行：`pip install -r requirements.txt`

### 完整诊断流程
1. 运行 `python check_config.py` - 检查环境配置
2. 尝试快速模式 `python run_sales_expert.py quick` - 测试基本功能
3. 如果问题仍然存在，检查网络和API服务状态

## 📞 技术支持

如遇到问题，请按以下顺序检查：
1. 运行配置检查脚本诊断问题
2. Python版本是否为3.8+
3. 依赖库是否正确安装
4. API密钥是否正确配置
5. 网络连接是否正常
6. 防火墙是否阻止了网络访问

---

🎉 **祝您使用愉快！** 如有问题或建议，欢迎反馈。
