#!/usr/bin/env python3
"""
测试默认模型配置
验证 meta-llama/llama-4-maverick 是否设置为默认模型
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_default_model():
    """测试默认模型配置"""
    print("🧪 测试默认模型配置")
    print("="*50)
    
    try:
        from sales_expert_standalone import SalesExpertStandalone
        
        # 创建实例
        sales_expert = SalesExpertStandalone()
        
        # 获取模型配置
        configs = sales_expert.get_model_configs()
        
        print("📋 可用模型配置:")
        for i, (model_name, config) in enumerate(configs.items(), 1):
            print(f"  {i}. {model_name} - {config['description']}")
        
        # 检查默认模型
        print(f"\n🎯 检查默认模型...")
        
        # 测试不传参数时的默认行为
        import inspect
        signature = inspect.signature(sales_expert.get_model_client)
        default_model = signature.parameters['model_name'].default
        
        print(f"📋 默认模型参数: {default_model}")
        
        if default_model == "meta-llama/llama-4-maverick":
            print("✅ 默认模型配置正确: meta-llama/llama-4-maverick")
        else:
            print(f"❌ 默认模型配置错误: 期望 meta-llama/llama-4-maverick，实际 {default_model}")
            return False
        
        # 检查模型是否在配置中
        if default_model in configs:
            config = configs[default_model]
            print(f"✅ 默认模型在配置中存在")
            print(f"   提供商: {config['provider']}")
            print(f"   API密钥: {config['api_key_env']}")
            print(f"   Base URL: {config['base_url']}")
            print(f"   描述: {config['description']}")
        else:
            print(f"❌ 默认模型不在配置中")
            return False
        
        # 检查模型顺序（应该是第一个）
        first_model = list(configs.keys())[0]
        if first_model == default_model:
            print("✅ 默认模型在配置列表中排第一位")
        else:
            print(f"⚠️ 默认模型不在第一位，第一位是: {first_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_fallback_model():
    """测试备用模型配置"""
    print("\n🧪 测试备用模型配置")
    print("="*50)
    
    try:
        from sales_expert_standalone import SalesExpertStandalone
        
        sales_expert = SalesExpertStandalone()
        configs = sales_expert.get_model_configs()
        
        # 测试不存在的模型名称
        print("📋 测试不存在的模型名称...")
        
        # 模拟get_model_client的逻辑
        test_model_name = "non-existent-model"
        if test_model_name not in configs:
            fallback_model = "meta-llama/llama-4-maverick"  # 这应该是代码中的备用模型
            print(f"✅ 不存在的模型 '{test_model_name}' 会回退到: {fallback_model}")
            
            if fallback_model in configs:
                print("✅ 备用模型在配置中存在")
                return True
            else:
                print("❌ 备用模型不在配置中")
                return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 默认模型配置测试")
    print("="*60)
    
    # 运行测试
    test1_ok = test_default_model()
    test2_ok = test_fallback_model()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    if test1_ok and test2_ok:
        print("🎉 所有测试通过！")
        print("✅ 默认模型已正确设置为 meta-llama/llama-4-maverick")
        print("✅ 备用模型配置正确")
        print("\n💡 现在可以使用以下命令测试:")
        print("   python run_sales_expert.py quick")
        print("   python sales_expert_standalone.py")
    else:
        print("❌ 部分测试失败")
        if not test1_ok:
            print("   - 默认模型配置有问题")
        if not test2_ok:
            print("   - 备用模型配置有问题")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        sys.exit(1)
