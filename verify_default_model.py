#!/usr/bin/env python3
"""
验证默认模型设置
简单验证脚本，确认默认模型已设置为 meta-llama/llama-4-maverick
"""

import sys
import inspect
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🔍 验证默认模型设置")
    print("="*50)
    
    try:
        # 导入销售专家类
        from sales_expert_standalone import SalesExpertStandalone
        
        # 创建实例
        sales_expert = SalesExpertStandalone()
        
        # 检查默认参数
        signature = inspect.signature(sales_expert.get_model_client)
        default_model = signature.parameters['model_name'].default
        
        print(f"📋 默认模型: {default_model}")
        
        # 检查模型配置
        configs = sales_expert.get_model_configs()
        first_model = list(configs.keys())[0]
        
        print(f"📋 配置中第一个模型: {first_model}")
        print(f"📋 模型描述: {configs[first_model]['description']}")
        
        # 验证结果
        if default_model == "meta-llama/llama-4-maverick" and first_model == "meta-llama/llama-4-maverick":
            print("\n✅ 验证成功！")
            print("✅ 默认模型已正确设置为 meta-llama/llama-4-maverick")
            print("✅ 该模型在配置列表中排第一位")
            print("\n🎯 现在可以使用工具了:")
            print("   • 快速模式: python run_sales_expert.py quick")
            print("   • 完整模式: python sales_expert_standalone.py")
            print("   • Windows用户: 双击 启动销售专家.bat")
        else:
            print("\n❌ 验证失败")
            print(f"   期望默认模型: meta-llama/llama-4-maverick")
            print(f"   实际默认模型: {default_model}")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")

if __name__ == "__main__":
    main()
