#!/usr/bin/env python3
"""
配置检查脚本
帮助用户验证销售专家工具的环境配置
"""

import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("   ✅ Python版本符合要求 (3.8+)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.8或更高版本")
        return False

def check_required_files():
    """检查必需文件"""
    print("\n📁 检查必需文件...")
    
    required_files = [
        ("sales_expert_standalone.py", "主程序文件"),
        ("run_sales_expert.py", "快速启动脚本"),
        ("MCP.env", "环境配置文件"),
        ("test_sales_expert.py", "测试脚本"),
        ("README_销售专家独立工具.md", "使用说明")
    ]
    
    all_files_exist = True
    for filename, description in required_files:
        if Path(filename).exists():
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ❌ {filename} - {description} (缺失)")
            all_files_exist = False
    
    return all_files_exist

def check_api_keys():
    """检查API密钥配置"""
    print("\n🔑 检查API密钥配置...")
    
    # 加载环境变量
    load_dotenv(dotenv_path="MCP.env")
    
    # 必需的API密钥
    required_keys = {
        "TAVILY_API_KEY": "Tavily搜索API (必需)"
    }
    
    # 可选的API密钥
    optional_keys = {
        "OPENROUTER_API_KEY": "OpenRouter API (推荐，支持Llama 4 Maverick)",
        "GROK_API_KEY": "xAI Grok模型 (备选)",
        "OPENAI_API_KEY": "OpenAI GPT模型 (备选)",
        "DEEPSEEK_API_KEY": "DeepSeek模型 (可选)",
        "ALIYUN_API_KEY": "阿里云通义千问 (可选)"
    }
    
    print("   必需的API密钥:")
    required_ok = True
    for key, description in required_keys.items():
        value = os.getenv(key)
        if value and len(value.strip()) > 10:
            print(f"     ✅ {key} - {description}")
        else:
            print(f"     ❌ {key} - {description} (未配置或无效)")
            required_ok = False
    
    print("\n   可选的API密钥:")
    available_models = []
    for key, description in optional_keys.items():
        value = os.getenv(key)
        if value and len(value.strip()) > 10:
            print(f"     ✅ {key} - {description}")
            if key == "OPENROUTER_API_KEY":
                available_models.append("Llama 4 Maverick")
            elif key == "GROK_API_KEY":
                available_models.append("Grok-4")
            elif key == "OPENAI_API_KEY":
                available_models.append("GPT-4o")
            elif key == "DEEPSEEK_API_KEY":
                available_models.append("DeepSeek")
            elif key == "ALIYUN_API_KEY":
                available_models.append("通义千问")
        else:
            print(f"     ⚠️ {key} - {description} (未配置)")
    
    if available_models:
        print(f"\n   📊 可用的AI模型: {', '.join(available_models)}")
    else:
        print(f"\n   ⚠️ 没有配置任何AI模型API密钥")
        required_ok = False
    
    return required_ok, available_models

def check_dependencies():
    """检查依赖库"""
    print("\n📦 检查依赖库...")
    
    required_packages = [
        ("dotenv", "python-dotenv", "环境变量加载"),
        ("autogen_core", "autogen-core", "AutoGen核心库"),
        ("autogen_agentchat", "autogen-agentchat", "AutoGen聊天代理"),
        ("autogen_ext", "autogen-ext", "AutoGen扩展工具")
    ]
    
    missing_packages = []
    for module_name, package_name, description in required_packages:
        try:
            __import__(module_name)
            print(f"   ✅ {module_name} - {description}")
        except ImportError:
            print(f"   ❌ {module_name} - {description} (缺失)")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n   📋 需要安装的包:")
        for package in missing_packages:
            print(f"     pip install {package}")
    
    return len(missing_packages) == 0

def check_network_connectivity():
    """检查网络连接"""
    print("\n🌐 检查网络连接...")
    
    try:
        import urllib.request
        import socket
        
        # 测试基本网络连接
        socket.create_connection(("*******", 53), timeout=5)
        print("   ✅ 基本网络连接正常")
        
        # 测试HTTPS连接
        try:
            urllib.request.urlopen("https://www.google.com", timeout=10)
            print("   ✅ HTTPS连接正常")
            return True
        except:
            print("   ⚠️ HTTPS连接可能有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ 网络连接失败: {e}")
        return False

def generate_config_template():
    """生成配置模板"""
    print("\n📝 生成配置模板...")
    
    template_content = """# 销售专家工具API密钥配置
# 请将 your_api_key_here 替换为实际的API密钥

# 必需 - Tavily搜索API密钥
TAVILY_API_KEY=your_tavily_api_key_here

# 推荐 - OpenRouter API密钥 (支持Llama 4 Maverick，性能最佳)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 备选 - xAI Grok模型API密钥
GROK_API_KEY=your_grok_api_key_here

# 备选 - OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here

# 可选 - DeepSeek API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 可选 - 阿里云通义千问API密钥
ALIYUN_API_KEY=your_aliyun_api_key_here

# 其他配置保持不变...
"""
    
    try:
        with open("MCP.env.template", "w", encoding="utf-8") as f:
            f.write(template_content)
        print("   ✅ 配置模板已生成: MCP.env.template")
        print("   💡 请复制此文件为 MCP.env 并填入真实的API密钥")
        return True
    except Exception as e:
        print(f"   ❌ 生成配置模板失败: {e}")
        return False

def show_recommendations(python_ok, files_ok, api_ok, deps_ok, network_ok, available_models):
    """显示建议"""
    print("\n" + "="*60)
    print("📊 配置检查总结")
    print("="*60)
    
    if all([python_ok, files_ok, api_ok, deps_ok, network_ok]):
        print("🎉 所有检查通过！工具可以正常使用")
        print(f"\n📋 可用的AI模型: {', '.join(available_models)}")
        print("\n🚀 现在可以开始使用:")
        print("   完整模式: python sales_expert_standalone.py")
        print("   快速模式: python run_sales_expert.py quick")
        print("   或双击: 启动销售专家.bat")
    else:
        print("⚠️ 发现以下问题需要解决:")
        
        if not python_ok:
            print("\n🐍 Python版本问题:")
            print("   - 请升级到Python 3.8或更高版本")
            print("   - 下载地址: https://www.python.org/downloads/")
        
        if not files_ok:
            print("\n📁 文件缺失问题:")
            print("   - 请确保所有必需文件都存在")
            print("   - 重新下载完整的工具包")
        
        if not api_ok:
            print("\n🔑 API密钥问题:")
            print("   - 请在 MCP.env 文件中配置必需的API密钥")
            print("   - 至少需要配置 TAVILY_API_KEY")
            print("   - 建议配置 GROK_API_KEY 或 OPENAI_API_KEY")
        
        if not deps_ok:
            print("\n📦 依赖库问题:")
            print("   - 请安装缺失的Python包")
            print("   - 运行: pip install -r requirements.txt")
        
        if not network_ok:
            print("\n🌐 网络连接问题:")
            print("   - 请检查网络连接")
            print("   - 确保可以访问外部API服务")
    
    print("="*60)

def main():
    """主函数"""
    print("🔧 销售专家工具配置检查")
    print("="*60)
    
    # 运行各项检查
    python_ok = check_python_version()
    files_ok = check_required_files()
    api_ok, available_models = check_api_keys()
    deps_ok = check_dependencies()
    network_ok = check_network_connectivity()
    
    # 如果MCP.env不存在，生成模板
    if not Path("MCP.env").exists():
        generate_config_template()
    
    # 显示建议
    show_recommendations(python_ok, files_ok, api_ok, deps_ok, network_ok, available_models)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {str(e)}")
        sys.exit(1)
