# 🚀 新模型配置说明

## ✨ 配置概览

已成功重新设计模型配置，按照您的要求分为6个系列，总共34个模型：

### 📂 模型系列配置

#### 1. ChatGPT系列 (8个模型)
**API配置**: `OPENROUTER_API_KEY` (OpenRouter)
- `openai/o3` - OpenAI最新O3模型，推理能力极强
- `openai/gpt-4.1` - GPT-4.1，增强版本
- `openai/gpt-4.1-mini` - GPT-4.1轻量版，快速响应
- `openai/gpt-4.1-nano` - GPT-4.1超轻量版
- `openai/gpt-4o-mini-search-preview` - GPT-4o Mini搜索预览版
- `openai/gpt-4o-search-preview` - GPT-4o搜索预览版
- `openai/gpt-4o` - GPT-4o，多模态能力强
- `openai/gpt-4o-mini` - GPT-4o轻量版

#### 2. <PERSON>系列 (7个模型)
**API配置**: `OPENROUTER_API_KEY` (OpenRouter)
- `anthropic/claude-opus-4` - <PERSON> 4，最强推理能力
- `anthropic/claude-sonnet-4` - Claude Sonnet 4，平衡性能
- `anthropic/claude-3.7-sonnet` - Claude 3.7 Sonnet
- `anthropic/claude-3.7-sonnet:beta` - Claude 3.7 Sonnet测试版
- `anthropic/claude-3.7-sonnet:thinking` - Claude 3.7 Sonnet思维版
- `anthropic/claude-3.5-haiku:beta` - Claude 3.5 Haiku测试版
- `anthropic/claude-3.5-haiku` - Claude 3.5 Haiku，快速响应

#### 3. Llama系列 (5个模型)
**API配置**: `OPENROUTER_API_KEY` (OpenRouter)
- `meta-llama/llama-4-maverick` - Llama 4 Maverick，专为商机分析优化（推荐）
- `meta-llama/llama-4-scout` - Llama 4 Scout，探索版本
- `thedrummer/anubis-70b-v1.1` - Anubis 70B，专业分析模型
- `nvidia/llama-3.1-nemotron-ultra-253b-v1:free` - Nvidia Llama 3.1 Nemotron Ultra免费版
- `nvidia/llama-3.1-nemotron-ultra-253b-v1` - Nvidia Llama 3.1 Nemotron Ultra

#### 4. Gemini系列 (5个模型)
**API配置**: `OPENROUTER_API_KEY` (OpenRouter)
- `google/gemini-2.5-flash-lite` - Gemini 2.5 Flash轻量版
- `google/gemini-2.5-flash-lite-preview-06-17` - Gemini 2.5 Flash Lite预览版
- `google/gemini-2.5-flash` - Gemini 2.5 Flash，快速响应
- `google/gemini-2.5-pro` - Gemini 2.5 Pro，专业版本
- `google/gemini-2.5-pro-preview` - Gemini 2.5 Pro预览版

#### 5. Qwen系列 (7个模型)
**API配置**: `ALIYUN_API_KEY` (阿里云直连)
- `qwen-max` - 通义千问最强版本
- `qwen-plus` - 通义千问增强版
- `qwen-turbo` - 通义千问快速版
- `qwq-plus` - QwQ Plus推理模型
- `qwen3-235b-a22b-thinking-2507` - Qwen3 235B思维版
- `qwen3-235b-a22b-instruct-2507` - Qwen3 235B指令版
- `qwen3-32b` - Qwen3 32B模型

#### 6. DeepSeek系列 (2个模型)
**API配置**: `DEEPSEEK_API_KEY` (DeepSeek直连)
- `deepseek-chat` - DeepSeek聊天模型
- `deepseek-reasoner` - DeepSeek推理模型，专业分析能力强

## 🔧 技术实现

### 1. 系列化配置架构
```python
# 新的配置结构
def get_model_series(self) -> Dict[str, Dict[str, Any]]:
    return {
        "ChatGPT系列": {
            "provider": "openrouter",
            "api_key_env": "OPENROUTER_API_KEY",
            "base_url": "https://openrouter.ai/api/v1",
            "models": { ... }
        },
        # 其他系列...
    }
```

### 2. 智能菜单系统
- **两级选择**: 先选择系列，再选择具体模型
- **详细信息**: 显示模型描述、API配置、能力特性
- **用户友好**: 清晰的分组和编号

### 3. 兼容性保证
- **向后兼容**: 保持原有的扁平化配置接口
- **自动转换**: 系列配置自动转换为扁平配置
- **错误处理**: 完善的API切换和错误恢复

## 🚀 使用方法

### 1. 启动工具
```bash
python sales_expert_standalone.py
```

### 2. 选择模型
1. 首先选择模型系列（1-6）
2. 然后选择具体模型（根据系列内的选项）
3. 系统会显示选择的模型信息

### 3. 示例选择流程
```
📂 可用模型系列:
1. ChatGPT系列 (8个模型) - 使用OPENROUTER
2. Claude系列 (7个模型) - 使用OPENROUTER
3. Llama系列 (5个模型) - 使用OPENROUTER
4. Gemini系列 (5个模型) - 使用OPENROUTER
5. Qwen系列 (7个模型) - 使用ALIYUN
6. DeepSeek系列 (2个模型) - 使用DEEPSEEK

请选择模型系列 (1-6): 1

📋 ChatGPT系列 可用模型:
1. OpenAI O3
   ID: openai/o3
   描述: OpenAI最新O3模型，推理能力极强

2. GPT-4.1
   ID: openai/gpt-4.1
   描述: GPT-4.1，增强版本
...

请选择具体模型 (1-8): 1
✅ 已选择: ChatGPT系列 -> OpenAI O3
   模型ID: openai/o3
```

## 📋 API配置要求

确保在 `MCP.env` 文件中配置以下API密钥：

```env
# 必需 - Tavily搜索API
TAVILY_API_KEY=your_tavily_api_key_here

# OpenRouter API (支持ChatGPT、Claude、Llama、Gemini系列)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 阿里云API (支持Qwen系列)
ALIYUN_API_KEY=your_aliyun_api_key_here

# DeepSeek API (支持DeepSeek系列)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

## 💡 推荐使用

### 1. 首次使用推荐
- **ChatGPT系列**: `openai/gpt-4o` - 稳定可靠
- **Claude系列**: `anthropic/claude-3.5-haiku` - 快速响应
- **Llama系列**: `meta-llama/llama-4-maverick` - 专为分析优化
- **DeepSeek系列**: `deepseek-reasoner` - 推理能力强

### 2. 不同场景推荐
- **复杂分析**: Claude Opus 4, OpenAI O3
- **快速响应**: GPT-4o Mini, Claude Haiku
- **推理任务**: DeepSeek Reasoner, QwQ Plus
- **多语言**: Qwen系列模型

## 🎯 优势特点

1. **系列化管理**: 按厂商和功能分组，便于选择
2. **统一API**: 多个系列共用OpenRouter，简化配置
3. **智能切换**: 支持系列内和跨系列的模型切换
4. **详细信息**: 每个模型都有清晰的描述和能力说明
5. **扩展性强**: 易于添加新的系列和模型

现在您可以享受更丰富、更有组织的模型选择体验！
