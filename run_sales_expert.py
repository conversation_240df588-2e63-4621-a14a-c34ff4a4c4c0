#!/usr/bin/env python3
"""
销售专家独立工具启动脚本
简化版启动器，提供快速分析功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from sales_expert_standalone import SalesExpertStandalone
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 sales_expert_standalone.py 文件存在")
    sys.exit(1)


async def quick_analysis():
    """快速分析模式"""
    print("🚀 销售专家快速分析模式")
    print("="*50)
    
    try:
        # 创建销售专家实例
        sales_expert = SalesExpertStandalone()
        
        # 获取产品信息（与原系统保持一致的默认值）
        print("📋 请输入分析信息（直接回车使用默认值）:")

        # 原系统的默认值
        default_product_name = "化学品合成设备"
        default_product_details = "应用于生物制药的微流"
        default_countries = "美国"
        default_time_period = "2025年7月"
        default_opportunities_count = 5

        product_name_input = input(f"产品名称 (默认: {default_product_name}): ").strip()
        product_name = product_name_input if product_name_input else default_product_name

        product_details_input = input(f"产品详情 (默认: {default_product_details}): ").strip()
        product_details = product_details_input if product_details_input else default_product_details

        countries_input = input(f"目标国家/地区 (默认: {default_countries}): ").strip()
        countries = countries_input if countries_input else default_countries

        time_period_input = input(f"检索时间段 (默认: {default_time_period}): ").strip()
        time_period = time_period_input if time_period_input else default_time_period

        opportunities_input = input(f"期望商机数量 (默认: {default_opportunities_count}): ").strip()
        try:
            opportunities_count = int(opportunities_input) if opportunities_input else default_opportunities_count
        except ValueError:
            opportunities_count = default_opportunities_count
        
        # 使用默认模型和模板
        print("\n🎯 使用默认配置进行分析...")
        print("📋 模型: meta-llama/llama-4-maverick (如果可用，否则使用 GPT-4o)")
        print("📋 模板: 默认销售专家模板")

        # 尝试使用Llama 4 Maverick，如果失败则使用GPT-4o
        model_name = "meta-llama/llama-4-maverick"
        try:
            model_client = sales_expert.get_model_client(model_name)
        except ValueError:
            print("⚠️ Llama 4 Maverick模型不可用，切换到GPT-4o")
            model_name = "openai/gpt-4o"
            model_client = sales_expert.get_model_client(model_name)
        
        # 显示确认信息
        print(f"\n✅ 分析参数确认:")
        print(f"   产品名称: {product_name}")
        print(f"   产品详情: {product_details}")
        print(f"   目标地区: {countries}")
        print(f"   时间段: {time_period}")
        print(f"   商机数量: {opportunities_count}")

        # 运行分析
        await sales_expert.run_analysis(
            sales_expert.default_template,
            product_name,
            product_details,
            countries,
            model_name,
            time_period,
            opportunities_count
        )
        
    except Exception as e:
        print(f"❌ 快速分析失败: {str(e)}")


def show_help():
    """显示帮助信息"""
    print("""
🤖 销售专家独立工具使用说明
="*50)

📋 运行模式:
1. 完整模式: python sales_expert_standalone.py
   - 提供完整的菜单界面
   - 支持自定义模板管理
   - 支持多种AI模型选择

2. 快速模式: python run_sales_expert.py quick
   - 快速分析，使用默认配置
   - 适合简单的一次性分析

3. 帮助模式: python run_sales_expert.py help
   - 显示此帮助信息

📋 环境要求:
- Python 3.8+
- 已安装必要的依赖库
- 配置好 MCP.env 文件中的API密钥

📋 API密钥配置 (MCP.env):
- TAVILY_API_KEY: 必需，用于网站搜索
- GROK_API_KEY: 推荐，xAI Grok模型
- OPENAI_API_KEY: 备选，OpenAI GPT模型
- DEEPSEEK_API_KEY: 可选，DeepSeek模型
- ALIYUN_API_KEY: 可选，阿里云通义千问

📋 输出文件:
- 分析结果保存在 analysis_results/ 目录
- 自定义模板保存在 sales_expert_templates.json

📋 示例用法:
python run_sales_expert.py quick
# 然后按提示输入产品信息即可

💡 提示:
- 首次使用建议先运行完整模式熟悉功能
- 确保网络连接正常，分析需要在线搜索
- 分析时间通常为2-5分钟，请耐心等待
""")


async def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "quick":
            await quick_analysis()
        elif command == "help":
            show_help()
        else:
            print(f"❌ 未知命令: {command}")
            print("可用命令: quick, help")
            print("或直接运行: python sales_expert_standalone.py")
    else:
        # 默认运行完整模式
        print("🎯 启动完整模式...")
        print("提示: 使用 'python run_sales_expert.py quick' 可快速分析")
        print("     使用 'python run_sales_expert.py help' 查看帮助")
        
        try:
            sales_expert = SalesExpertStandalone()
            await sales_expert.main_loop()
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行失败: {str(e)}")
        sys.exit(1)
