#!/usr/bin/env python3
"""
销售专家独立工具使用示例
演示如何在代码中直接调用销售专家功能
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from sales_expert_standalone import SalesExpertStandalone
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 sales_expert_standalone.py 文件存在")
    sys.exit(1)


async def example_analysis():
    """示例分析"""
    print("🎯 销售专家分析示例")
    print("="*50)
    
    try:
        # 创建销售专家实例
        print("🔧 初始化销售专家...")
        sales_expert = SalesExpertStandalone()
        
        # 示例产品信息（与原系统保持一致的默认值）
        product_name = "化学品合成设备"
        product_details = "应用于生物制药的微流"
        countries = "美国"
        time_period = "2025年7月"
        opportunities_count = 5
        
        print(f"📋 产品信息:")
        print(f"   产品名称: {product_name}")
        print(f"   产品详情: {product_details}")
        print(f"   目标地区: {countries}")
        print(f"   时间段: {time_period}")
        print(f"   商机数量: {opportunities_count}")
        
        # 选择模型（优先使用Llama 4 Maverick，如果不可用则使用GPT-4o）
        model_name = "meta-llama/llama-4-maverick"
        try:
            print(f"\n🤖 尝试使用模型: {model_name}")
            model_client = sales_expert.get_model_client(model_name)
        except ValueError as e:
            print(f"⚠️ {model_name} 不可用: {e}")
            model_name = "openai/gpt-4o"
            print(f"🔄 切换到备用模型: {model_name}")
            try:
                model_client = sales_expert.get_model_client(model_name)
            except ValueError as e:
                print(f"❌ 所有模型都不可用: {e}")
                print("请检查 MCP.env 文件中的API密钥配置")
                return
        
        # 运行分析
        print(f"\n🚀 开始分析...")
        await sales_expert.run_analysis(
            sales_expert.default_template,
            product_name,
            product_details,
            countries,
            model_name,
            time_period,
            opportunities_count
        )
        
        print("\n✅ 示例分析完成！")
        
    except Exception as e:
        print(f"❌ 示例分析失败: {str(e)}")


async def example_custom_template():
    """自定义模板示例"""
    print("\n🎯 自定义模板示例")
    print("="*50)
    
    # 创建一个简化的自定义模板（包含完整变量）
    custom_template = """You are a sales expert. Analyze the product {product_name} for {countries} market in {time_period}.

Product Details: {product_details}
Target Period: {time_period}
Expected Opportunities: {opportunities_count}

Please provide:
1. Target market analysis for {countries}
2. Key industry websites (use tavily-search to find real URLs)
3. Potential sales channels
4. Market opportunities for {time_period}

Use tavily-search tool to find relevant websites and provide real URLs."""
    
    try:
        sales_expert = SalesExpertStandalone()
        
        # 保存自定义模板
        sales_expert.save_custom_template("简化分析模板", custom_template)
        
        print("✅ 自定义模板已保存")
        print("📋 模板内容预览:")
        print("-" * 30)
        print(custom_template[:200] + "...")
        
        # 可以选择运行自定义模板分析
        print("\n💡 提示: 您可以在主程序中选择使用此自定义模板进行分析")
        
    except Exception as e:
        print(f"❌ 自定义模板示例失败: {str(e)}")


def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用方法示例")
    print("="*50)
    
    examples = [
        {
            "title": "快速分析",
            "command": "python run_sales_expert.py quick",
            "description": "使用默认配置快速分析"
        },
        {
            "title": "完整模式",
            "command": "python sales_expert_standalone.py",
            "description": "启动完整的交互界面"
        },
        {
            "title": "查看帮助",
            "command": "python run_sales_expert.py help",
            "description": "显示详细的使用说明"
        },
        {
            "title": "测试环境",
            "command": "python test_sales_expert.py",
            "description": "测试环境配置和依赖"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
        print()


async def main():
    """主函数"""
    print("🤖 销售专家独立工具使用示例")
    print("="*60)
    
    # 显示使用示例
    show_usage_examples()
    
    # 询问是否运行示例
    try:
        choice = input("是否运行分析示例？(y/N): ").strip().lower()
        
        if choice == 'y':
            # 运行示例分析
            await example_analysis()
            
            # 创建自定义模板示例
            await example_custom_template()
        else:
            print("👋 跳过示例运行")
            
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    
    print("\n" + "="*60)
    print("📋 更多使用方法:")
    print("   完整模式: python sales_expert_standalone.py")
    print("   快速模式: python run_sales_expert.py quick")
    print("   查看帮助: python run_sales_expert.py help")
    print("="*60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
        sys.exit(1)
