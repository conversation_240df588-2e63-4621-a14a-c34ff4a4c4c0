#!/usr/bin/env python3
"""
仅测试导入 - 验证导入问题是否修复
"""

print("🧪 测试导入修复...")

try:
    print("1. 测试 autogen_ext.models.openai 导入...")
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    print("   ✅ OpenAIChatCompletionClient 导入成功")
    
    print("2. 测试 autogen_core.models 导入...")
    from autogen_core.models import ModelInfo, ModelCapabilities
    print("   ✅ ModelInfo, ModelCapabilities 导入成功")
    
    print("3. 测试其他依赖导入...")
    from autogen_agentchat.agents import AssistantAgent
    from autogen_ext.tools.mcp import StdioServerParams, mcp_server_tools
    print("   ✅ 其他依赖导入成功")
    
    print("\n🎉 所有导入测试通过！")
    print("✅ 导入问题已修复")
    
    # 测试基本实例化
    print("\n4. 测试基本功能...")
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    
    from sales_expert_standalone import SalesExpertStandalone
    sales_expert = SalesExpertStandalone()
    configs = sales_expert.get_model_configs()
    default_model = list(configs.keys())[0]
    
    print(f"   ✅ 默认模型: {default_model}")
    print(f"   ✅ 模型配置数量: {len(configs)}")
    
    print("\n🎉 全部测试通过！")
    print("✅ 销售专家工具已准备就绪")
    print("✅ 默认模型已设置为 meta-llama/llama-4-maverick")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请检查依赖库是否正确安装")
except Exception as e:
    print(f"❌ 其他错误: {e}")
    print("请检查代码配置")
