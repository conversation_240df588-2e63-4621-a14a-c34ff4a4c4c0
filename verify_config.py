#!/usr/bin/env python3
"""
验证API配置脚本
确认当前配置是否正确
"""

import os
from dotenv import load_dotenv

def main():
    print("🔍 验证API配置...")
    print("="*50)
    
    # 加载环境变量
    load_dotenv(dotenv_path="MCP.env")
    
    # 检查配置
    print("📋 当前API配置状态:")
    
    # OpenRouter API (统一入口)
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    if openrouter_key:
        print("✅ OPENROUTER_API_KEY: 已配置")
        print("   支持模型:")
        print("   - meta-llama/llama-4-maverick (默认)")
        print("   - openai/gpt-4o")
        print("   - x-ai/grok-4-0709")
    else:
        print("❌ OPENROUTER_API_KEY: 未配置")
    
    # DeepSeek API (直连)
    deepseek_key = os.getenv("DEEPSEEK_API_KEY")
    if deepseek_key:
        print("✅ DEEPSEEK_API_KEY: 已配置")
        print("   支持模型: deepseek-reasoner")
    else:
        print("❌ DEEPSEEK_API_KEY: 未配置")
    
    # 阿里云API (直连)
    aliyun_key = os.getenv("ALIYUN_API_KEY")
    if aliyun_key:
        print("✅ ALIYUN_API_KEY: 已配置")
        print("   支持模型: qwen-max")
    else:
        print("❌ ALIYUN_API_KEY: 未配置")
    
    # Tavily API (必需)
    tavily_key = os.getenv("TAVILY_API_KEY")
    if tavily_key:
        print("✅ TAVILY_API_KEY: 已配置 (搜索工具)")
    else:
        print("❌ TAVILY_API_KEY: 未配置 (必需)")
    
    print("\n" + "="*50)
    
    # 总结
    available_count = sum([
        1 if openrouter_key else 0,
        1 if deepseek_key else 0,
        1 if aliyun_key else 0
    ])
    
    if tavily_key and available_count > 0:
        print("🎉 配置验证通过！")
        print(f"📊 可用API服务: {available_count} 个")
        print("🚀 现在可以运行销售专家工具:")
        print("   python sales_expert_standalone.py")
    else:
        print("⚠️ 配置不完整")
        if not tavily_key:
            print("   - 缺少 TAVILY_API_KEY (必需)")
        if available_count == 0:
            print("   - 没有可用的AI模型API")
    
    print("="*50)

if __name__ == "__main__":
    main()
