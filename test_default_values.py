#!/usr/bin/env python3
"""
测试默认值设置
验证产品信息的默认值是否与原系统一致
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_default_values():
    """测试默认值设置"""
    print("🧪 测试默认值设置")
    print("="*50)
    
    try:
        from sales_expert_standalone import SalesExpertStandalone
        
        # 创建实例
        sales_expert = SalesExpertStandalone()
        
        # 检查默认模型
        configs = sales_expert.get_model_configs()
        default_model = list(configs.keys())[0]
        print(f"✅ 默认模型: {default_model}")
        
        # 模拟获取产品信息的默认值（通过检查代码）
        print("\n📋 检查产品信息默认值:")
        
        # 从代码中提取默认值
        import inspect
        source = inspect.getsource(sales_expert.get_product_info)
        
        # 检查默认值是否存在
        expected_defaults = {
            "default_product_name": "化学品合成设备",
            "default_product_details": "应用于生物制药的微流",
            "default_countries": "美国",
            "default_time_period": "2025年7月",
            "default_opportunities_count": "5"
        }
        
        all_defaults_found = True
        for key, expected_value in expected_defaults.items():
            if expected_value in source:
                print(f"   ✅ {key}: {expected_value}")
            else:
                print(f"   ❌ {key}: 未找到期望值 {expected_value}")
                all_defaults_found = False
        
        # 检查模板变量
        print("\n📋 检查模板变量支持:")
        template = sales_expert.default_template
        
        template_vars = [
            "{product_name}",
            "{product_details}",
            "{countries}"
        ]
        
        for var in template_vars:
            if var in template:
                print(f"   ✅ 模板支持变量: {var}")
            else:
                print(f"   ❌ 模板缺少变量: {var}")
        
        return all_defaults_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_template_formatting():
    """测试模板格式化"""
    print("\n🧪 测试模板格式化")
    print("="*50)
    
    try:
        from sales_expert_standalone import SalesExpertStandalone
        
        sales_expert = SalesExpertStandalone()
        
        # 测试参数
        test_params = {
            "product_name": "化学品合成设备",
            "product_details": "应用于生物制药的微流",
            "countries": "美国",
            "time_period": "2025年7月",
            "opportunities_count": 5
        }
        
        print("📋 测试参数:")
        for key, value in test_params.items():
            print(f"   {key}: {value}")
        
        # 测试格式化
        formatted = sales_expert.format_template(
            sales_expert.default_template,
            **test_params
        )
        
        if formatted and len(formatted) > 100:
            print("✅ 模板格式化成功")
            print(f"   格式化后长度: {len(formatted)}")
            
            # 检查关键内容是否存在
            key_elements = ["化学品合成设备", "美国", "2025年7月"]
            for element in key_elements:
                if element in formatted:
                    print(f"   ✅ 包含关键元素: {element}")
                else:
                    print(f"   ⚠️ 缺少关键元素: {element}")
            
            return True
        else:
            print("❌ 模板格式化失败")
            return False
            
    except Exception as e:
        print(f"❌ 模板格式化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 默认值一致性测试")
    print("="*60)
    
    # 运行测试
    defaults_ok = test_default_values()
    formatting_ok = test_template_formatting()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    if defaults_ok and formatting_ok:
        print("🎉 所有测试通过！")
        print("✅ 默认值设置与原系统一致")
        print("✅ 模板格式化功能正常")
        print("✅ 默认模型设置为 meta-llama/llama-4-maverick")
        print("\n🚀 现在可以使用工具了:")
        print("   • 快速模式: python run_sales_expert.py quick")
        print("   • 完整模式: python sales_expert_standalone.py")
        print("   • Windows用户: 双击 启动销售专家.bat")
        print("\n💡 提示: 所有输入都有默认值，可以直接回车快速开始")
    else:
        print("❌ 部分测试失败")
        if not defaults_ok:
            print("   - 默认值设置有问题")
        if not formatting_ok:
            print("   - 模板格式化有问题")
    
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        sys.exit(1)
