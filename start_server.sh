#!/bin/bash

# 智能商机分析系统 - 服务器启动脚本
# 适用于阿里云服务器等Linux环境

echo "🚀 启动智能商机分析系统（公网访问模式）"
echo "================================================"

# 检查Python环境
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

python_version=$(python3 --version 2>&1)
echo "✅ Python环境: $python_version"

# 检查环境配置文件
echo "📋 检查环境配置..."
if [ ! -f "MCP.env" ]; then
    echo "❌ 未找到 MCP.env 文件"
    echo "请确保环境配置文件存在"
    exit 1
fi
echo "✅ 环境配置文件存在"

# 检查主程序文件
if [ ! -f "module2.py" ]; then
    echo "❌ 未找到 module2.py 文件"
    echo "请确保主程序文件存在"
    exit 1
fi
echo "✅ 主程序文件存在"

# 检查端口占用
echo "📋 检查端口7860..."
if netstat -tlnp 2>/dev/null | grep -q ":7860 "; then
    echo "⚠️ 端口7860已被占用"
    echo "正在尝试终止占用进程..."
    pkill -f "module2.py" 2>/dev/null || true
    sleep 2
    
    if netstat -tlnp 2>/dev/null | grep -q ":7860 "; then
        echo "❌ 无法释放端口7860，请手动处理"
        echo "使用命令查看占用进程: netstat -tlnp | grep :7860"
        exit 1
    fi
fi
echo "✅ 端口7860可用"

# 获取服务器信息
echo "📋 获取服务器信息..."
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "未知")
PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "未知")

echo "🌐 服务器信息:"
echo "   内网IP: $LOCAL_IP"
echo "   公网IP: $PUBLIC_IP"

# 显示访问信息
echo ""
echo "================================================"
echo "📡 访问信息"
echo "================================================"
echo "🏠 本地访问: http://127.0.0.1:7860"
echo "🌐 内网访问: http://$LOCAL_IP:7860"
if [ "$PUBLIC_IP" != "未知" ]; then
    echo "🌍 公网访问: http://$PUBLIC_IP:7860"
fi
echo "🔗 Gradio分享: 启动后自动生成"
echo ""
echo "💡 提醒:"
echo "   • 确保阿里云安全组已开放7860端口"
echo "   • Gradio分享链接支持全球访问"
echo "   • 公网访问请注意安全防护"
echo "================================================"

# 创建日志目录
mkdir -p logs

# 启动应用
echo "🚀 启动应用..."
echo "📝 日志将保存到 logs/app.log"
echo "🔄 使用 Ctrl+C 停止应用"
echo ""

# 启动应用并记录日志
python3 module2.py 2>&1 | tee logs/app.log
