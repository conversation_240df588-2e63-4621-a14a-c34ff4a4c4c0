🤖 销售专家独立分析工具 - 快速上手指南
================================================================

📋 工具简介
----------------------------------------------------------------
这是一个基于原有销售专家功能开发的独立命令行工具，无需启动Web界面，
支持默认模板和自定义模板，可以快速分析产品的目标市场网站推荐。

🚀 快速开始（3步搞定）
----------------------------------------------------------------
1. 检查配置：双击运行 "启动销售专家.bat"，选择 "3. 测试环境"
2. 配置API密钥：编辑 MCP.env 文件，至少配置 TAVILY_API_KEY
3. 开始分析：双击运行 "启动销售专家.bat"，选择 "2. 快速模式"

📁 主要文件说明
----------------------------------------------------------------
启动销售专家.bat          - Windows用户双击启动（推荐）
sales_expert_standalone.py - 主程序（完整功能）
run_sales_expert.py       - 快速启动脚本
check_config.py           - 配置检查工具
MCP.env                   - API密钥配置文件
README_销售专家独立工具.md - 详细使用说明

🔑 必需的API密钥
----------------------------------------------------------------
TAVILY_API_KEY     - 必需，用于网站搜索
OPENROUTER_API_KEY - 推荐，支持Llama 4 Maverick模型（性能最佳）
GROK_API_KEY       - 备选，xAI Grok模型
OPENAI_API_KEY     - 备选，OpenAI GPT模型

💡 使用建议
----------------------------------------------------------------
1. 首次使用建议先运行配置检查
2. 推荐使用Llama 4 Maverick模型，专为商机分析优化
3. 分析时间通常2-5分钟，请耐心等待
4. 结果会自动保存到 analysis_results/ 目录

🎯 典型使用流程
----------------------------------------------------------------
1. 双击 "启动销售专家.bat"
2. 选择 "2. 快速模式"
3. 输入产品信息（直接回车使用默认值）：
   - 产品名称（默认：化学品合成设备）
   - 产品详情（默认：应用于生物制药的微流）
   - 目标地区（默认：美国）
   - 时间段（默认：2025年7月）
   - 商机数量（默认：5）
4. 等待分析完成
5. 查看结果文件

💡 默认值说明
----------------------------------------------------------------
系统提供了与原版本一致的默认值，可以直接回车快速开始分析：
- 默认产品：化学品合成设备（生物制药应用）
- 默认地区：美国市场
- 默认时间：2025年7月
- 默认商机数量：5个

📞 遇到问题？
----------------------------------------------------------------
1. 运行配置检查：python check_config.py
2. 查看详细说明：README_销售专家独立工具.md
3. 测试基本功能：python test_sales_expert.py

🎉 开始使用吧！
================================================================
